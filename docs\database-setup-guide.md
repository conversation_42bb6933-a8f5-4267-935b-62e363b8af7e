# 数据库设置指南 / Database Setup Guide

## 概述 / Overview

本指南将帮助你设置玄学网站的数据库系统。我们使用 PostgreSQL 作为主数据库，Prisma 作为 ORM（对象关系映射）工具。

## 数据库架构说明 / Database Architecture

### 为什么选择这个架构？

1. **PostgreSQL** - 强大的关系型数据库
   - 支持复杂查询和事务
   - 优秀的性能和可扩展性
   - 支持 JSON 数据类型（用于存储测试结果）
   - 免费且开源

2. **Prisma ORM** - 现代化的数据库工具
   - 类型安全的数据库操作
   - 自动生成的客户端代码
   - 简单的数据库迁移管理
   - 优秀的开发体验

3. **Supabase** - 数据库托管服务
   - 免费的 PostgreSQL 托管
   - 内置认证和实时功能
   - 简单的设置和管理
   - 自动备份

## 设置步骤 / Setup Steps

### 第一步：创建 Supabase 项目

1. 访问 [Supabase](https://supabase.com)
2. 注册账户并创建新项目
3. 记录以下信息：
   - Project URL: `https://[YOUR_PROJECT_REF].supabase.co`
   - API Key (anon): `your-anon-key`
   - Database Password: `your-database-password`

### 第二步：配置环境变量

1. 复制 `.env.example` 为 `.env.local`：
   ```bash
   cp .env.example .env.local
   ```

2. 更新 `.env.local` 中的数据库配置：
   ```env
   DATABASE_URL="postgresql://postgres:[YOUR_PASSWORD]@db.[YOUR_PROJECT_REF].supabase.co:5432/postgres?schema=public"
   DIRECT_URL="postgresql://postgres:[YOUR_PASSWORD]@db.[YOUR_PROJECT_REF].supabase.co:5432/postgres?schema=public"
   ```

### 第三步：安装依赖并生成 Prisma 客户端

```bash
# 安装依赖
npm install

# 生成 Prisma 客户端
npm run db:generate
```

### 第四步：创建数据库表

```bash
# 创建并应用数据库迁移
npx prisma migrate dev --name init
```

### 第五步：播种初始数据

```bash
# 运行种子脚本
npm run db:seed
```

## 数据库表结构 / Database Schema

### 主要表格说明

1. **users** - 用户表
   - 存储用户基本信息
   - 支持多语言偏好设置
   - 主题偏好（浅色/深色）

2. **blog_posts** - 博客文章表
   - 支持多语言内容
   - SEO 优化字段
   - 富文本内容存储
   - 分类和标签系统

3. **test_results** - 测试结果表
   - 存储各种玄学测试结果
   - JSON 格式存储答案和结果
   - 支持结果分享功能

4. **comments** - 评论表
   - 支持嵌套回复
   - 评论审核功能

5. **user_favorites** - 用户收藏表
   - 用户收藏的文章

6. **blog_views** - 浏览记录表
   - 文章浏览统计
   - 支持游客和用户浏览

## 常用数据库操作 / Common Database Operations

### 查询博客文章

```typescript
import { getBlogPosts } from '@/lib/database'

// 获取中文博客文章
const posts = await getBlogPosts({
  locale: 'zh-CN',
  category: 'tarot',
  page: 1,
  limit: 10
})
```

### 创建测试结果

```typescript
import { createTestResult } from '@/lib/database'

const result = await createTestResult({
  testType: 'TAROT',
  answers: { cards: ['the-fool', 'the-magician'] },
  result: { interpretation: '新的开始...' },
  isPublic: true
})
```

### 记录文章浏览

```typescript
import { recordBlogView } from '@/lib/database'

await recordBlogView(postId, userId, ipAddress, userAgent)
```

## 数据库管理 / Database Management

### 查看数据库

使用 Prisma Studio（可视化数据库管理工具）：

```bash
npx prisma studio
```

这会在浏览器中打开一个图形界面，你可以：
- 查看所有表格和数据
- 添加、编辑、删除记录
- 执行查询

### 数据库迁移

当你修改 `schema.prisma` 文件后：

```bash
# 创建新的迁移
npx prisma migrate dev --name describe-your-changes

# 应用迁移到生产环境
npx prisma migrate deploy
```

### 重置数据库

如果需要重新开始：

```bash
# 重置数据库并重新播种
npx prisma migrate reset
```

## 性能优化 / Performance Optimization

### 索引策略

数据库已经配置了以下索引：
- 博客文章：按语言、分类、状态、发布时间
- 用户：按邮箱、用户名
- 测试结果：按用户、测试类型、分享令牌

### 查询优化

- 使用 `include` 预加载相关数据
- 实现分页避免大量数据查询
- 使用 Redis 缓存热门内容

## 备份和恢复 / Backup and Recovery

### Supabase 自动备份

Supabase 提供自动备份功能：
- 每日自动备份
- 可以从控制面板恢复
- 支持时间点恢复

### 手动备份

```bash
# 导出数据库结构和数据
pg_dump $DATABASE_URL > backup.sql

# 恢复数据库
psql $DATABASE_URL < backup.sql
```

## 故障排除 / Troubleshooting

### 常见问题

1. **连接失败**
   - 检查 `DATABASE_URL` 是否正确
   - 确认 Supabase 项目状态
   - 检查网络连接

2. **迁移失败**
   - 检查数据库权限
   - 确认 schema 语法正确
   - 查看错误日志

3. **性能问题**
   - 检查查询是否使用了索引
   - 考虑添加缓存
   - 优化查询逻辑

### 获取帮助

- Prisma 文档: https://www.prisma.io/docs
- Supabase 文档: https://supabase.com/docs
- PostgreSQL 文档: https://www.postgresql.org/docs

## 下一步 / Next Steps

数据库设置完成后，你可以：

1. 开始创建 API 路由来操作数据
2. 构建前端组件来显示数据
3. 实现用户认证系统
4. 添加内容管理功能
5. 集成 AI 服务生成内容

数据库是你网站的基础，现在你已经有了一个强大且可扩展的数据存储系统！
