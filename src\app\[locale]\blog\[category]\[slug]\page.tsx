import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Image from 'next/image';
import Link from 'next/link';
import {
  ReadingProgress,
  TableOfContents,
  MobileTableOfContents,
  generateTableOfContents,
  FocusMode,
  SocialShare,
  ArticleInteractions
} from '@/components/blog';
import { BlogPost, Locale } from '@/types';
import { Heart, Share2, Tag } from 'lucide-react';
import { DatabaseService } from '@/lib/database-service';

interface BlogPostPageProps {
  params: {
    locale: string;
    category: string;
    slug: string;
  };
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = await getPostBySlug(params.category, params.slug, params.locale as Locale);

  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }

  const description = post.seoDescription || post.excerpt || '';

  return {
    title: post.seoTitle || post.title,
    description,
    keywords: post.keywords,
    authors: [{ name: 'Author' }],
    openGraph: {
      title: post.seoTitle || post.title,
      description,
      type: 'article',
      locale: params.locale,
      publishedTime: post.publishedAt?.toISOString(),
      modifiedTime: post.updatedAt.toISOString(),
      authors: ['Author'],
      images: post.coverImage ? [{ url: post.coverImage, alt: post.title }] : [],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.seoTitle || post.title,
      description,
      images: post.coverImage ? [post.coverImage] : [],
    },
  };
}

// 数据获取函数
async function getPostBySlug(
  category: string,
  slug: string,
  locale: Locale
): Promise<BlogPost | null> {
  try {
    const post = await DatabaseService.getBlogPostBySlug(slug, locale);

    // 验证分类是否匹配
    if (post && post.category !== category) {
      return null;
    }

    return post;
  } catch (error) {
    console.error('Error fetching post:', error);
    return null;
  }
}

async function getRelatedPosts(
  postId: string,
  categorySlug: string,
  locale: Locale,
  limit: number = 3
): Promise<BlogPost[]> {
  try {
    const posts = await DatabaseService.getBlogPosts({
      locale,
      status: 'PUBLISHED',
      category: categorySlug,
      limit: limit + 1, // 多获取一个，以防包含当前文章
    });

    // 排除当前文章
    return posts.filter(post => post.id !== postId).slice(0, limit);
  } catch (error) {
    console.error('Error fetching related posts:', error);
    return [];
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  const locale = params.locale as Locale;

  const post = await getPostBySlug(params.category, params.slug, locale);

  if (!post) {
    notFound();
  }

  const relatedPosts = await getRelatedPosts(post.id, params.category, locale);
  const tableOfContents = generateTableOfContents(post.content);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-dark-900">
      {/* 阅读进度指示器 */}
      <ReadingProgress target="article" variant="bar" />

      {/* 专注阅读模式控制 */}
      <FocusMode />

      {/* 浮动社交分享 */}
      <SocialShare
        url={`${process.env['NEXT_PUBLIC_SITE_URL'] || 'https://example.com'}/${locale}/blog/${post.category}/${post.slug}`}
        title={post.title}
        description={post.excerpt || ''}
        variant="floating"
      />

      {/* 文章互动浮动面板 */}
      <ArticleInteractions
        postId={post.id}
        initialLikes={post.likeCount}
        initialViews={post.viewCount}
        initialComments={post.commentCount}
        variant="floating"
      />

      {/* Medium风格的文章头部 */}
      <header className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-8">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-mystical-500 dark:text-mystical-400 mb-8">
          <Link href={`/${locale}/blog`} className="hover:text-mystical-700 dark:hover:text-mystical-300">
            Blog
          </Link>
          <span>/</span>
          <Link
            href={`/${locale}/blog?category=${post.category}`}
            className="hover:text-mystical-700 dark:hover:text-mystical-300 capitalize"
          >
            {post.category}
          </Link>
        </nav>

        {/* 文章标题 - Medium风格 */}
        <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold font-serif text-mystical-900 dark:text-white mb-6 leading-tight tracking-tight">
          {post.title}
        </h1>

        {/* 文章摘要 */}
        {post.excerpt && (
          <p className="text-xl text-mystical-600 dark:text-mystical-300 mb-8 leading-relaxed">
            {post.excerpt}
          </p>
        )}

        {/* 作者信息和元数据 - Medium风格 */}
        <div className="flex items-center justify-between py-4 border-b border-mystical-200 dark:border-dark-700">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-mystical-200 dark:bg-dark-700 rounded-full flex items-center justify-center">
              <span className="text-mystical-600 dark:text-mystical-400 font-medium">A</span>
            </div>
            <div>
              <p className="font-medium text-mystical-900 dark:text-white">
                Author
              </p>
              <div className="flex items-center gap-4 text-sm text-mystical-500 dark:text-mystical-400">
                <span>{formatDate(post.publishedAt || post.createdAt)}</span>
                <span>·</span>
                <span>{post.readingTime} min read</span>
              </div>
            </div>
          </div>

          {/* 互动按钮 */}
          <div className="flex items-center gap-2">
            <button className="p-2 rounded-full hover:bg-mystical-100 dark:hover:bg-dark-700 transition-colors">
              <Heart className="w-5 h-5 text-mystical-500 dark:text-mystical-400" />
            </button>
            <button className="p-2 rounded-full hover:bg-mystical-100 dark:hover:bg-dark-700 transition-colors">
              <Share2 className="w-5 h-5 text-mystical-500 dark:text-mystical-400" />
            </button>
          </div>
        </div>
      </header>

      {/* 封面图片 - Medium风格 */}
      {post.coverImage && (
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mb-12">
          <Image
            src={post.coverImage}
            alt={post.title}
            width={1200}
            height={675}
            className="w-full aspect-[16/9] object-cover rounded-lg"
            priority
          />
        </div>
      )}

      {/* 主要内容区域 - Medium风格布局 */}
      <div className="relative">
        {/* 浮动目录 - 仅在桌面端显示 */}
        {tableOfContents.length > 0 && (
          <TableOfContents
            items={tableOfContents}
            variant="floating"
            className="hidden xl:block fixed left-8 top-1/2 transform -translate-y-1/2 z-10"
          />
        )}

        {/* 文章内容容器 */}
        <div className="max-w-[680px] mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          {/* 移动端目录 */}
          <MobileTableOfContents items={tableOfContents} className="mb-8 xl:hidden" />

          {/* 文章正文 - 严格按照Medium规范 */}
          <article
            id="article"
            className="prose prose-lg max-w-none"
            style={{
              fontSize: '1.25rem',        // 20px - Medium标准字体大小
              lineHeight: '1.75',         // 1.75倍行高 - Medium标准
              color: 'var(--mystical-800)',
              fontFamily: 'Georgia, serif', // Medium使用的字体
            }}
          >
            {/* 文章内容样式 */}
            <style jsx>{`
              article h1, article h2, article h3, article h4, article h5, article h6 {
                font-family: 'Playfair Display', serif;
                font-weight: bold;
                color: var(--mystical-900);
                margin-top: 2rem;
                margin-bottom: 1rem;
                line-height: 1.2;
              }

              article h1 { font-size: 2.5rem; }
              article h2 { font-size: 2rem; }
              article h3 { font-size: 1.75rem; }
              article h4 { font-size: 1.5rem; }

              article p {
                margin-bottom: 1.5rem;
                line-height: 1.75;
              }

              article p:first-of-type {
                font-size: 1.375rem;
                font-weight: 400;
                color: var(--mystical-900);
              }

              article blockquote {
                font-size: 1.375rem;
                font-style: italic;
                line-height: 1.6;
                color: var(--mystical-700);
                padding: 1.5rem 2rem;
                margin: 2rem 0;
                background: var(--mystical-50);
                border-left: 4px solid var(--mystical-400);
                border-radius: 0 0.5rem 0.5rem 0;
              }

              article img {
                width: 100%;
                height: auto;
                border-radius: 0.5rem;
                margin: 2rem 0;
              }

              article a {
                color: var(--mystical-600);
                text-decoration: underline;
                text-decoration-color: var(--mystical-300);
                text-underline-offset: 0.2em;
              }

              article a:hover {
                color: var(--mystical-700);
                text-decoration-color: var(--mystical-500);
              }
            `}</style>

            <div dangerouslySetInnerHTML={{ __html: post.content }} />
          </article>

          {/* 文章底部 */}
          <div className="mt-16">
            <ArticleFooter post={post} relatedPosts={relatedPosts} />
          </div>
        </div>
      </div>
    </div>
  );
}

// 文章底部组件 - Medium风格
function ArticleFooter({
  post,
  relatedPosts
}: {
  post: BlogPost;
  relatedPosts: BlogPost[]
}) {
  return (
    <div className="space-y-12 border-t border-mystical-200 dark:border-dark-700 pt-12">
      {/* 标签 */}
      {post.tags.length > 0 && (
        <div>
          <h3 className="text-lg font-bold text-mystical-900 dark:text-white mb-4">
            Topics
          </h3>
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag, index) => (
              <Link
                key={index}
                href={`/blog?tag=${tag}`}
                className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm bg-mystical-100 dark:bg-dark-700 text-mystical-700 dark:text-mystical-300 hover:bg-mystical-200 dark:hover:bg-dark-600 transition-colors"
              >
                <Tag className="w-3 h-3" />
                {tag}
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* 文章互动 - Medium风格 */}
      <ArticleInteractions
        postId={post.id}
        initialLikes={post.likeCount}
        initialViews={post.viewCount}
        initialComments={post.commentCount}
        variant="inline"
      />

      {/* 作者简介 - Medium风格 */}
      <div className="flex items-start gap-4 py-6">
        <div className="w-16 h-16 bg-mystical-200 dark:bg-dark-700 rounded-full flex items-center justify-center">
          <span className="text-mystical-600 dark:text-mystical-400 font-medium text-lg">A</span>
        </div>
        <div className="flex-1">
          <h4 className="text-lg font-bold text-mystical-900 dark:text-white mb-2">
            Author
          </h4>
          <p className="text-mystical-600 dark:text-mystical-300 mb-3 leading-relaxed">
            Professional writer and mystical knowledge expert. Passionate about sharing insights on tarot, astrology, and spiritual growth.
          </p>
          <button className="text-sm font-medium text-mystical-600 dark:text-mystical-400 hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors">
            Follow
          </button>
        </div>
      </div>

      {/* 相关文章 - Medium风格 */}
      {relatedPosts.length > 0 && (
        <div>
          <h3 className="text-2xl font-bold text-mystical-900 dark:text-white mb-6">
            More from this topic
          </h3>
          <div className="space-y-6">
            {relatedPosts.map((relatedPost) => (
              <Link
                key={relatedPost.id}
                href={`/blog/${relatedPost.category}/${relatedPost.slug}`}
                className="group block"
              >
                <div className="flex gap-4 p-4 rounded-lg hover:bg-mystical-50 dark:hover:bg-dark-800 transition-colors">
                  {relatedPost.coverImage && (
                    <div className="flex-shrink-0">
                      <Image
                        src={relatedPost.coverImage}
                        alt={relatedPost.title}
                        width={120}
                        height={80}
                        className="w-30 h-20 object-cover rounded"
                      />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-mystical-900 dark:text-white mb-2 line-clamp-2 group-hover:text-mystical-700 dark:group-hover:text-mystical-300 transition-colors">
                      {relatedPost.title}
                    </h4>
                    <p className="text-sm text-mystical-600 dark:text-mystical-400 line-clamp-2 mb-2">
                      {relatedPost.excerpt}
                    </p>
                    <div className="text-xs text-mystical-500 dark:text-mystical-400">
                      {relatedPost.readingTime} min read
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
