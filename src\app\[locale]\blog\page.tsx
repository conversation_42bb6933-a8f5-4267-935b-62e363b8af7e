import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import { Suspense } from 'react';
import { BlogList, BlogListSkeleton } from '@/components/blog';
import { BlogPost, BlogCategory, BlogTag, Locale } from '@/types';
import { DatabaseService } from '@/lib/database-service';

interface BlogPageProps {
  params: {
    locale: string;
  };
  searchParams: {
    page?: string;
    category?: string;
    tag?: string;
    search?: string;
  };
}

export async function generateMetadata({ params }: BlogPageProps): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  
  return {
    title: t('title'),
    description: t('description'),
    keywords: [
      t('keywords.mystical'),
      t('keywords.tarot'),
      t('keywords.astrology'),
      t('keywords.numerology'),
      t('keywords.crystal'),
      t('keywords.palmistry'),
      t('keywords.dreams'),
    ],
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale: params.locale,
    },
  };
}

// 获取博客数据的函数
async function getBlogData(
  locale: Locale,
  page: number = 1,
  limit: number = 12,
  filters?: {
    category?: string;
    tag?: string;
    search?: string;
  }
): Promise<{
  posts: BlogPost[];
  featuredPost?: BlogPost;
  totalPages: number;
  categories: BlogCategory[];
  tags: BlogTag[];
  totalPosts: number;
}> {
  try {
    // 获取分类和标签
    const [categories, tags] = await Promise.all([
      DatabaseService.getBlogCategories(locale),
      DatabaseService.getBlogTags(locale),
    ]);

    // 计算偏移量
    const offset = (page - 1) * limit;

    // 获取特色文章（仅在第一页显示）
    let featuredPost: BlogPost | undefined;
    if (page === 1) {
      const featuredPosts = await DatabaseService.getBlogPosts({
        locale,
        status: 'PUBLISHED',
        featured: true,
        limit: 1,
        orderBy: 'published_at',
        orderDirection: 'desc',
      });
      featuredPost = featuredPosts[0];
    }

    // 获取常规文章列表
    const posts = await DatabaseService.getBlogPosts({
      locale,
      status: 'PUBLISHED',
      category: filters?.category,
      featured: false, // 排除特色文章
      limit,
      offset,
      orderBy: 'published_at',
      orderDirection: 'desc',
    });

    // 获取总数用于分页
    const allPosts = await DatabaseService.getBlogPosts({
      locale,
      status: 'PUBLISHED',
      category: filters?.category,
      featured: false,
    });
    const totalPosts = allPosts.length;
    const totalPages = Math.ceil(totalPosts / limit);

    return {
      posts,
      featuredPost,
      totalPages,
      categories,
      tags,
      totalPosts,
    };
  } catch (error) {
    console.error('Error fetching blog data:', error);
    // 返回空数据作为fallback
    return {
      posts: [],
      featuredPost: undefined,
      totalPages: 1,
      categories: [],
      tags: [],
      totalPosts: 0,
    };
  }
}

export default async function BlogPage({ params, searchParams }: BlogPageProps) {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  const locale = params.locale as Locale;

  const page = parseInt(searchParams.page || '1', 10);
  const { posts, featuredPost, totalPages, categories, tags, totalPosts } = await getBlogData(
    locale,
    page,
    12,
    {
      category: searchParams.category,
      tag: searchParams.tag,
      search: searchParams.search,
    }
  );

  return (
    <div className="min-h-screen bg-white dark:bg-dark-900">
      {/* 页面头部 - 基于01规范的设计 */}
      <header className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-8">
          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold font-serif text-mystical-900 dark:text-white mb-4 tracking-tight">
            {t('title')}
          </h1>
          <p className="text-lg sm:text-xl text-mystical-600 dark:text-mystical-300 max-w-2xl mx-auto leading-relaxed">
            {t('description')}
          </p>
        </div>

        {/* 分类导航 - Medium风格 */}
        <nav className="flex flex-wrap justify-center gap-2 mb-8">
          <a
            href={`/${locale}/blog`}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
              !searchParams.category
                ? 'bg-mystical-500 text-white'
                : 'bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400 hover:bg-mystical-200 dark:hover:bg-dark-600'
            }`}
          >
            {t('allCategories')}
          </a>
          {categories.map((category) => (
            <a
              key={category.id}
              href={`/${locale}/blog?category=${category.slug}`}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                searchParams.category === category.slug
                  ? 'bg-mystical-500 text-white'
                  : 'bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400 hover:bg-mystical-200 dark:hover:bg-dark-600'
              }`}
            >
              {category.name}
            </a>
          ))}
        </nav>
      </header>

      {/* 主要内容区域 - Medium风格布局 */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* 主内容区 */}
          <div className="lg:col-span-2">
            <Suspense fallback={<BlogListSkeleton showFeatured={!!featuredPost} />}>
              <BlogList
                posts={posts}
                featuredPost={featuredPost}
                showFeatured={page === 1}
                layout="medium" // 使用Medium风格布局
                totalPosts={totalPosts}
                currentPage={page}
                totalPages={totalPages}
              />
            </Suspense>
          </div>

          {/* 侧边栏 */}
          <aside className="lg:col-span-1">
            <div className="sticky top-8">
              <BlogSidebar
                categories={categories}
                tags={tags}
                locale={locale}
                currentCategory={searchParams.category}
              />
            </div>
          </aside>
        </div>
      </main>
    </div>
  );
}

// 博客侧边栏组件 - 基于01规范的Medium风格设计
function BlogSidebar({
  categories,
  tags,
  locale,
  currentCategory
}: {
  categories: BlogCategory[];
  tags: BlogTag[];
  locale: string;
  currentCategory?: string;
}) {
  return (
    <div className="space-y-6">
      {/* 搜索框 - Medium风格 */}
      <div className="relative">
        <input
          type="text"
          placeholder="Search articles..."
          className="w-full px-4 py-3 pl-10 text-sm border border-mystical-200 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-mystical-800 dark:text-mystical-200 placeholder-mystical-400 dark:placeholder-mystical-500 focus:outline-none focus:border-mystical-400 focus:ring-3 focus:ring-mystical-100 dark:focus:ring-mystical-900 transition-all"
        />
        <svg
          className="absolute left-3 top-3.5 w-4 h-4 text-mystical-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>

      {/* 热门文章 */}
      <div>
        <h3 className="text-lg font-bold text-mystical-900 dark:text-white mb-4">
          Popular Posts
        </h3>
        <div className="space-y-4">
          {/* 这里应该显示热门文章，暂时用占位符 */}
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="flex gap-3">
              <span className="text-sm font-bold text-mystical-500 min-w-[20px]">
                {i.toString().padStart(2, '0')}
              </span>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-mystical-800 dark:text-mystical-200 line-clamp-2 leading-5 mb-1">
                  Sample Article Title {i}
                </h4>
                <p className="text-xs text-mystical-500 dark:text-mystical-400">
                  5 min read
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 标签云 */}
      <div>
        <h3 className="text-lg font-bold text-mystical-900 dark:text-white mb-4">
          Topics
        </h3>
        <div className="flex flex-wrap gap-2">
          {tags.slice(0, 12).map((tag) => (
            <a
              key={tag.id}
              href={`/${locale}/blog?tag=${tag.slug}`}
              className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400 hover:bg-mystical-200 dark:hover:bg-dark-600 transition-colors"
            >
              {tag.name}
            </a>
          ))}
        </div>
      </div>

      {/* 订阅表单 - Medium风格 */}
      <div className="bg-mystical-50 dark:bg-dark-800 p-6 rounded-xl border border-mystical-200 dark:border-dark-700">
        <h3 className="text-lg font-bold text-mystical-900 dark:text-white mb-2">
          Stay Updated
        </h3>
        <p className="text-sm text-mystical-600 dark:text-mystical-300 mb-4 leading-relaxed">
          Get the latest mystical insights and test content delivered to your inbox.
        </p>
        <form className="space-y-3">
          <input
            type="email"
            placeholder="Enter your email"
            className="w-full px-3 py-2 text-sm border border-mystical-200 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-mystical-800 dark:text-mystical-200 placeholder-mystical-400 dark:placeholder-mystical-500 focus:outline-none focus:border-mystical-400 focus:ring-3 focus:ring-mystical-100 dark:focus:ring-mystical-900 transition-all"
          />
          <button
            type="submit"
            className="w-full px-4 py-2 text-sm font-medium text-white bg-mystical-500 rounded-lg hover:bg-mystical-600 focus:outline-none focus:ring-3 focus:ring-mystical-100 dark:focus:ring-mystical-900 transition-all"
          >
            Subscribe
          </button>
        </form>
      </div>
    </div>
  );
}
