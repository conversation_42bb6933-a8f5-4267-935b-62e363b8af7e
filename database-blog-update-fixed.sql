-- 博客系统数据库结构更新脚本（修正版）
-- 基于现有database-setup.sql表结构进行优化
-- 适配camelCase命名规范

-- 1. 更新博客文章表，添加缺失的字段
ALTER TABLE blog_posts 
ADD COLUMN IF NOT EXISTS "scheduledAt" TIMESTAMP(3),
ADD COLUMN IF NOT EXISTS "featured" BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS "likeCount" INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS "shareCount" INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS "commentCount" INTEGER DEFAULT 0;

-- 2. 创建博客分类表
CREATE TABLE IF NOT EXISTS blog_categories (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(120) UNIQUE NOT NULL,
    description VARCHAR(500),
    color VARCHAR(7), -- HEX颜色代码
    icon VARCHAR(50),
    image VARCHAR(500),
    locale VARCHAR(10) NOT NULL,
    "postCount" INTEGER DEFAULT 0,
    "seoTitle" VARCHAR(60),
    "seoDescription" VARCHAR(160),
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP
);

-- 3. 创建博客标签表
CREATE TABLE IF NOT EXISTS blog_tags (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    name VARCHAR(50) NOT NULL,
    slug VARCHAR(60) UNIQUE NOT NULL,
    description VARCHAR(200),
    color VARCHAR(7), -- HEX颜色代码
    locale VARCHAR(10) NOT NULL,
    "postCount" INTEGER DEFAULT 0,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP
);

-- 4. 创建新的索引优化查询性能
CREATE INDEX IF NOT EXISTS "blog_posts_featured_status_publishedAt_idx" 
ON blog_posts(featured, status, "publishedAt");

CREATE INDEX IF NOT EXISTS "blog_categories_locale_idx" 
ON blog_categories(locale);

CREATE INDEX IF NOT EXISTS "blog_categories_slug_idx" 
ON blog_categories(slug);

CREATE INDEX IF NOT EXISTS "blog_tags_locale_idx" 
ON blog_tags(locale);

CREATE INDEX IF NOT EXISTS "blog_tags_slug_idx" 
ON blog_tags(slug);

-- 5. 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 6. 为相关表添加更新时间戳触发器
DROP TRIGGER IF EXISTS update_blog_categories_updated_at ON blog_categories;
CREATE TRIGGER update_blog_categories_updated_at 
    BEFORE UPDATE ON blog_categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_blog_tags_updated_at ON blog_tags;
CREATE TRIGGER update_blog_tags_updated_at 
    BEFORE UPDATE ON blog_tags 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. 插入默认的博客分类数据
INSERT INTO blog_categories (name, slug, description, color, icon, locale, "seoTitle", "seoDescription") 
VALUES 
    ('塔罗牌', 'tarot', '塔罗牌占卜、解读和指南', '#8B5CF6', 'cards', 'zh-CN', '塔罗牌占卜指南', '专业的塔罗牌占卜指南，包含牌意解读、占卜方法和实用技巧'),
    ('星座占星', 'astrology', '星座运势、性格分析和占星学知识', '#F59E0B', 'star', 'zh-CN', '星座占星学大全', '深入了解12星座特点、运势分析和占星学知识'),
    ('数字命理', 'numerology', '数字命理学、生命数字计算和解读', '#10B981', 'hash', 'zh-CN', '数字命理学入门', '探索数字的神秘力量，学习生命数字计算和命理解读'),
    ('水晶能量', 'crystal', '水晶疗愈、能量石和水晶知识', '#EC4899', 'gem', 'zh-CN', '水晶能量疗愈', '了解各种水晶的能量属性和疗愈功效'),
    ('手相学', 'palmistry', '手相解读、掌纹分析和手相学知识', '#F97316', 'hand', 'zh-CN', '手相学解读指南', '学习手相解读技巧，了解掌纹的含义和预测方法'),
    ('梦境解析', 'dreams', '梦境解读、梦的象征意义和解梦技巧', '#6366F1', 'moon', 'zh-CN', '梦境解析大全', '深入解读梦境的象征意义，掌握科学的解梦方法')
ON CONFLICT (slug) DO NOTHING;

-- 8. 插入英文版本的分类
INSERT INTO blog_categories (name, slug, description, color, icon, locale, "seoTitle", "seoDescription") 
VALUES 
    ('Tarot', 'tarot', 'Tarot card readings, interpretations and guides', '#8B5CF6', 'cards', 'en', 'Tarot Card Reading Guide', 'Professional tarot card reading guides with card meanings, divination methods and practical tips'),
    ('Astrology', 'astrology', 'Horoscopes, personality analysis and astrological knowledge', '#F59E0B', 'star', 'en', 'Complete Astrology Guide', 'Explore the 12 zodiac signs, horoscope analysis and astrological wisdom'),
    ('Numerology', 'numerology', 'Numerology, life path numbers and interpretations', '#10B981', 'hash', 'en', 'Numerology Basics', 'Discover the mystical power of numbers and learn life path calculations'),
    ('Crystal Healing', 'crystal', 'Crystal healing, energy stones and crystal knowledge', '#EC4899', 'gem', 'en', 'Crystal Energy Healing', 'Learn about crystal properties and healing benefits'),
    ('Palmistry', 'palmistry', 'Palm reading, line analysis and palmistry knowledge', '#F97316', 'hand', 'en', 'Palmistry Reading Guide', 'Master palm reading techniques and understand the meaning of palm lines'),
    ('Dream Analysis', 'dreams', 'Dream interpretation, symbolism and dream analysis techniques', '#6366F1', 'moon', 'en', 'Dream Analysis Guide', 'Decode dream symbolism and master scientific dream interpretation methods')
ON CONFLICT (slug) DO NOTHING;

-- 9. 插入一些常用标签
INSERT INTO blog_tags (name, slug, locale) 
VALUES 
    ('初学者指南', 'beginner-guide', 'zh-CN'),
    ('进阶技巧', 'advanced-tips', 'zh-CN'),
    ('实用方法', 'practical-methods', 'zh-CN'),
    ('心理学', 'psychology', 'zh-CN'),
    ('灵性成长', 'spiritual-growth', 'zh-CN'),
    ('Beginner Guide', 'beginner-guide', 'en'),
    ('Advanced Tips', 'advanced-tips', 'en'),
    ('Practical Methods', 'practical-methods', 'en'),
    ('Psychology', 'psychology', 'en'),
    ('Spiritual Growth', 'spiritual-growth', 'en')
ON CONFLICT (slug) DO NOTHING;

-- 10. 创建一个函数来增加浏览量
CREATE OR REPLACE FUNCTION increment_view_count(post_id TEXT)
RETURNS VOID AS $$
BEGIN
    UPDATE blog_posts 
    SET "viewCount" = "viewCount" + 1 
    WHERE id = post_id;
END;
$$ LANGUAGE plpgsql;

-- 完成提示
SELECT 'Blog system database update completed successfully!' as message;
