/**
 * 数据库服务层 - 基于Supabase API的数据库操作
 * 遵循database-management.md中的最佳实践
 */

import { supabase } from './supabase';
import type { BlogPost, BlogCategory, BlogTag } from '@/types';

export class DatabaseService {
  /**
   * 博客文章相关操作
   */
  static async createBlogPost(data: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt'>) {
    const { data: post, error } = await supabase
      .from('blog_posts')
      .insert({
        title: data.title,
        slug: data.slug,
        content: data.content,
        excerpt: data.excerpt,
        cover_image: data.coverImage,
        locale: data.locale,
        category: data.category,
        tags: data.tags,
        status: data.status,
        published_at: data.publishedAt,
        scheduled_at: data.scheduledAt,
        reading_time: data.readingTime,
        featured: data.featured,
        seo_title: data.seoTitle,
        seo_description: data.seoDescription,
        keywords: data.keywords,
        metadata: data.metadata,
      })
      .select()
      .single();

    if (error) throw error;
    return this.transformBlogPost(post);
  }

  static async getBlogPosts(options: {
    locale?: string;
    status?: string;
    category?: string;
    featured?: boolean;
    limit?: number;
    offset?: number;
    orderBy?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}) {
    let query = supabase.from('blog_posts').select('*');

    if (options.locale) {
      query = query.eq('locale', options.locale);
    }
    if (options.status) {
      query = query.eq('status', options.status);
    }
    if (options.category) {
      query = query.eq('category', options.category);
    }
    if (options.featured !== undefined) {
      query = query.eq('featured', options.featured);
    }

    // 排序
    const orderBy = options.orderBy || 'published_at';
    const orderDirection = options.orderDirection || 'desc';
    query = query.order(orderBy, { ascending: orderDirection === 'asc' });

    // 分页
    if (options.limit) {
      query = query.limit(options.limit);
    }
    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data?.map(post => this.transformBlogPost(post)) || [];
  }

  static async getBlogPostBySlug(slug: string, locale?: string) {
    let query = supabase
      .from('blog_posts')
      .select('*')
      .eq('slug', slug);

    if (locale) {
      query = query.eq('locale', locale);
    }

    const { data, error } = await query.single();

    if (error) throw error;
    return this.transformBlogPost(data);
  }

  static async updateBlogPost(id: string, data: Partial<BlogPost>) {
    const updateData: any = {};
    
    // 映射字段名
    if (data.title !== undefined) updateData.title = data.title;
    if (data.slug !== undefined) updateData.slug = data.slug;
    if (data.content !== undefined) updateData.content = data.content;
    if (data.excerpt !== undefined) updateData.excerpt = data.excerpt;
    if (data.coverImage !== undefined) updateData.cover_image = data.coverImage;
    if (data.category !== undefined) updateData.category = data.category;
    if (data.tags !== undefined) updateData.tags = data.tags;
    if (data.status !== undefined) updateData.status = data.status;
    if (data.publishedAt !== undefined) updateData.published_at = data.publishedAt;
    if (data.scheduledAt !== undefined) updateData.scheduled_at = data.scheduledAt;
    if (data.featured !== undefined) updateData.featured = data.featured;
    if (data.seoTitle !== undefined) updateData.seo_title = data.seoTitle;
    if (data.seoDescription !== undefined) updateData.seo_description = data.seoDescription;
    if (data.keywords !== undefined) updateData.keywords = data.keywords;
    if (data.metadata !== undefined) updateData.metadata = data.metadata;

    const { data: post, error } = await supabase
      .from('blog_posts')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return this.transformBlogPost(post);
  }

  static async deleteBlogPost(id: string) {
    const { error } = await supabase
      .from('blog_posts')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  static async incrementViewCount(id: string) {
    const { error } = await supabase.rpc('increment_view_count', { post_id: id });
    if (error) throw error;
  }

  /**
   * 博客分类相关操作
   */
  static async getBlogCategories(locale?: string) {
    let query = supabase.from('blog_categories').select('*');
    
    if (locale) {
      query = query.eq('locale', locale);
    }

    const { data, error } = await query.order('name');

    if (error) throw error;
    return data?.map(cat => this.transformBlogCategory(cat)) || [];
  }

  static async getBlogCategoryBySlug(slug: string, locale?: string) {
    let query = supabase
      .from('blog_categories')
      .select('*')
      .eq('slug', slug);

    if (locale) {
      query = query.eq('locale', locale);
    }

    const { data, error } = await query.single();

    if (error) throw error;
    return this.transformBlogCategory(data);
  }

  /**
   * 博客标签相关操作
   */
  static async getBlogTags(locale?: string) {
    let query = supabase.from('blog_tags').select('*');
    
    if (locale) {
      query = query.eq('locale', locale);
    }

    const { data, error } = await query.order('name');

    if (error) throw error;
    return data?.map(tag => this.transformBlogTag(tag)) || [];
  }

  /**
   * 搜索功能
   */
  static async searchBlogPosts(query: string, locale?: string, limit = 10) {
    let searchQuery = supabase
      .from('blog_posts')
      .select('*')
      .or(`title.ilike.%${query}%,content.ilike.%${query}%,excerpt.ilike.%${query}%`)
      .eq('status', 'PUBLISHED')
      .limit(limit);

    if (locale) {
      searchQuery = searchQuery.eq('locale', locale);
    }

    const { data, error } = await searchQuery.order('published_at', { ascending: false });

    if (error) throw error;
    return data?.map(post => this.transformBlogPost(post)) || [];
  }

  /**
   * 数据转换方法 - 将数据库字段映射到TypeScript接口
   */
  private static transformBlogPost(post: any): BlogPost {
    return {
      id: post.id,
      title: post.title,
      slug: post.slug,
      content: post.content,
      excerpt: post.excerpt,
      coverImage: post.cover_image,
      locale: post.locale,
      category: post.category,
      tags: post.tags || [],
      status: post.status,
      publishedAt: post.published_at ? new Date(post.published_at) : undefined,
      scheduledAt: post.scheduled_at ? new Date(post.scheduled_at) : undefined,
      readingTime: post.reading_time || 0,
      viewCount: post.view_count || 0,
      likeCount: post.like_count || 0,
      shareCount: post.share_count || 0,
      commentCount: post.comment_count || 0,
      featured: post.featured || false,
      seoTitle: post.seo_title,
      seoDescription: post.seo_description,
      keywords: post.keywords || [],
      metadata: post.metadata,
      createdAt: new Date(post.created_at),
      updatedAt: new Date(post.updated_at),
    };
  }

  private static transformBlogCategory(category: any): BlogCategory {
    return {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      color: category.color,
      icon: category.icon,
      image: category.image,
      locale: category.locale,
      postCount: category.post_count || 0,
      seoTitle: category.seo_title,
      seoDescription: category.seo_description,
      createdAt: new Date(category.created_at),
      updatedAt: new Date(category.updated_at),
    };
  }

  private static transformBlogTag(tag: any): BlogTag {
    return {
      id: tag.id,
      name: tag.name,
      slug: tag.slug,
      description: tag.description,
      color: tag.color,
      locale: tag.locale,
      postCount: tag.post_count || 0,
      createdAt: new Date(tag.created_at),
      updatedAt: new Date(tag.updated_at),
    };
  }
}
